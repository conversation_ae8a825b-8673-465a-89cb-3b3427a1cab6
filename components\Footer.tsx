import Image from 'next/image';
import Link from 'next/link';
import { FaMapMarkerAlt, FaPhone, FaEnvelope } from 'react-icons/fa';

export default function Footer() {
  return (
    <footer className="bg-primary-red text-primary-yellow">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Logo and Description */}
          <div className="space-y-4">
            <Image
              src="/logo/logo_footer.png"
              alt="Romoletto Logo"
              width={150}
              height={50}
              className="h-24 w-auto"
            />
            <p className="text-sm leading-relaxed">
              Romoletto: autentica cucina romana nel cuore di Roma. 
              Pizza, pasta e tradizione in un&apos;atmosfera familiare.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-heading text-lg font-semibold mb-4">Menu</h3>
            <ul className="space-y-2">
              <li>
                <Link 
                  href="/" 
                  className="text-sm hover:text-off-white transition-colors"
                >
                  Home
                </Link>
              </li>
              <li>
                <Link 
                  href="/nostra-storia" 
                  className="text-sm hover:text-off-white transition-colors"
                >
                  La Nostra Storia
                </Link>
              </li>
              <li>
                <Link 
                  href="/prenotazione" 
                  className="text-sm hover:text-off-white transition-colors"
                >
                  Prenotazione
                </Link>
              </li>
              <li>
                <Link 
                  href="/lavora-con-noi" 
                  className="text-sm hover:text-off-white transition-colors"
                >
                  Lavora con Noi
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-heading text-lg font-semibold mb-4">Contatti</h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <FaMapMarkerAlt className="w-4 h-4 flex-shrink-0" />
                <span>Piazza Campo de&apos; Fiori 47, 00186 Roma Italia</span>
              </div>
              <div className="flex items-center space-x-2">
                <FaPhone className="w-4 h-4 flex-shrink-0" />
                <span>+39 06 XXXX XXXX</span>
              </div>
              <div className="flex items-center space-x-2">
                <FaEnvelope className="w-4 h-4 flex-shrink-0" />
                <span><EMAIL></span>
              </div>
              <div className="pt-4">
                <h4 className="font-semibold mb-2">Orari di Apertura</h4>
                <p>Lun-Dom: 12:00 - 15:00</p>
                <p>Lun-Dom: 19:00 - 24:00</p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-primary-yellow border-opacity-30 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm">
              © {new Date().getFullYear()} Giuca S.R.L. - P.IVA 15883831008 - Romoletto. Tutti i diritti riservati.
            </p>
            <div className="flex space-x-6 text-sm">
              <Link 
                href="/privacy" 
                className="hover:text-off-white transition-colors"
              >
                Privacy Policy
              </Link>
              <Link 
                href="/cookie-policy" 
                className="hover:text-off-white transition-colors"
              >
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
